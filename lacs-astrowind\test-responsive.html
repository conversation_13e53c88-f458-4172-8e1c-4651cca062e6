<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .grid {
            display: grid;
            gap: 1rem;
            grid-template-columns: 1fr;
        }
        @media (min-width: 768px) {
            .grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }
        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-height: 180px;
            display: flex;
            flex-direction: column;
        }
        .card-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            flex-grow: 1;
        }
        .logo {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            margin-right: 12px;
            flex-shrink: 0;
            object-fit: cover;
        }
        @media (min-width: 768px) {
            .logo {
                width: 56px;
                height: 56px;
                margin-right: 16px;
            }
        }
        .info {
            flex-grow: 1;
            min-width: 0;
        }
        .name {
            font-size: 1rem;
            font-weight: bold;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        @media (min-width: 768px) {
            .name {
                font-size: 1.125rem;
            }
        }
        .account {
            font-size: 0.75rem;
            color: #666;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        @media (min-width: 768px) {
            .account {
                font-size: 0.875rem;
            }
        }
        .id {
            font-size: 0.75rem;
            color: #999;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .actions {
            display: flex;
            gap: 8px;
            margin-top: auto;
        }
        .btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }
        .btn-qr {
            background: #eff6ff;
            color: #2563eb;
        }
        .btn-qr:hover {
            background: #dbeafe;
        }
        .btn-visit {
            background: #2563eb;
            color: white;
        }
        .btn-visit:hover {
            background: #1d4ed8;
        }
        .icon {
            margin-right: 4px;
            font-size: 0.75rem;
        }
        @media (min-width: 768px) {
            .icon {
                font-size: 0.875rem;
            }
        }
        .text-sm {
            display: none;
        }
        @media (min-width: 640px) {
            .text-sm {
                display: inline;
            }
            .text-xs {
                display: none;
            }
        }
        .screen-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">屏幕尺寸: <span id="screenSize"></span></div>
    
    <div class="container">
        <h1>媒体平台响应式布局测试</h1>
        <p>调整浏览器窗口大小查看不同屏幕尺寸下的布局效果：</p>
        <ul>
            <li>移动设备 (&lt;768px): 单列布局</li>
            <li>平板设备 (768px-1023px): 双列布局</li>
            <li>桌面设备 (≥1024px): 三列布局</li>
        </ul>
        
        <div class="grid">
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/blibli.webp" alt="哔哩哔哩" class="logo">
                    <div class="info">
                        <div class="name">哔哩哔哩</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: **********</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/wechat.webp" alt="微信公众号" class="logo">
                    <div class="info">
                        <div class="name">微信公众号</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: lacsgf</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/coolapk.webp" alt="酷安" class="logo">
                    <div class="info">
                        <div class="name">酷安</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: ********</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/csdn.webp" alt="CSDN" class="logo">
                    <div class="info">
                        <div class="name">CSDN</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: qq_57391324</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/MT管理器.webp" alt="MT论坛" class="logo">
                    <div class="info">
                        <div class="name">MT论坛</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: 114957</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <img src="https://img.lacs.cc/img/25-07/05/MiBBS.webp" alt="小米社区" class="logo">
                    <div class="info">
                        <div class="name">小米社区</div>
                        <div class="account">@领创工作室</div>
                        <div class="id">ID: LCStudio</div>
                    </div>
                </div>
                <div class="actions">
                    <a href="#" class="btn btn-qr">
                        <span class="icon">📱</span>
                        <span class="text-sm">二维码</span>
                        <span class="text-xs">码</span>
                    </a>
                    <a href="#" class="btn btn-visit">
                        <span class="icon">🔗</span>
                        访问
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            let device = '';
            
            if (width < 768) {
                device = '移动设备 (单列)';
            } else if (width < 1024) {
                device = '平板设备 (双列)';
            } else {
                device = '桌面设备 (三列)';
            }
            
            document.getElementById('screenSize').textContent = `${width}×${height} - ${device}`;
        }
        
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
    </script>
</body>
</html>
